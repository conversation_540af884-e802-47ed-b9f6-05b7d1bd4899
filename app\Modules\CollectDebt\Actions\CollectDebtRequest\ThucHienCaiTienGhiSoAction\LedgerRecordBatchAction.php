<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\ThucHienCaiTienGhiSoAction;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtReadyRecord;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Model\CollectDebtStatisticPartner;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\ThucHienCaiTienGhiSoAction\SubAction\TaoBanGhiLedgerSubAction;

class LedgerRecordBatchAction extends BatchProcessingAction
{
	public $timeout = 90;

	public function __construct()
	{
		ini_set('max_execution_time', $this->timeout);
	}

	public function run()
	{
		$results = CollectDebtReadyRecord::query()
												->where('status', 1)
												->limit(config('nextlend.BATCHING_LIMIT'))
												->pluck('id')
												->toArray();

		if ( empty($results) ) {
			return ['msg' => 'No data for LedgerRecordBatchAction'];
		}

		$ranges = array_chunk($results, $this->batchSize);

		$this->processBatch($ranges);

		return ['msg' => 'ok done'];
	}

	private function processBatch($ranges)
	{
		$baseUrl = config('app.url');

		// Generator
		$requests = function () use ($ranges, $baseUrl) {
			foreach ($ranges as $r) {
				$url = sprintf('%s/ProcessRecored?ids=%s', $baseUrl, implode(',', $r));
				yield $r => new Request('GET', $url, []);
			}
		};

		$client = $this->createHttpClient($this->timeout - 10);

		$pool = new Pool($client, $requests(), [
			'concurrency' => $this->batchCount,
			'fulfilled' => function ($response, $r) {
				// $body = (string)$response->getBody();
			},
			'rejected' => function (\Throwable $reason, $r) {
				$msg = "[LedgerRecord ---> failed: " . $reason->getMessage();
				Log::info($msg);
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);

		return true;
	}

	public function ProcessRecored() 
	{
		$ids = request()->get('ids');
		$readyRecordIds = array_map('intval', explode(',', $ids));

		$listReadyRecord = CollectDebtReadyRecord::query()
			->with('collectDebtRequest')
			->whereIn('id', $readyRecordIds)
			->get();

		if ($listReadyRecord->isEmpty()) {
			return ['msg' => 'No data'];
		}

		foreach ($listReadyRecord as $rc) {
			if (!$rc->collectDebtRequest) {
				$this->deleteReadyRecord($rc->id, 4);
				continue;
			}

			try {
				$ycGhiSo = $this->HandleLedgerRecord($rc->collectDebtRequest, $rc->id);
				
				if (
					!empty(optional($ycGhiSo)->id) && 
					$ycGhiSo->status_recored == CollectDebtEnum::REQUEST_STT_RC_DA_GHI_SO
				) {
					$this->deleteReadyRecord($rc->id);
				}
			}catch(\Throwable $th) {
				
			}
		}

		return ['msg' => 'ok done'];
	}

	private function deleteReadyRecord(int $readyRecordId, $status=3)
	{
		return CollectDebtReadyRecord::query()->where('id', $readyRecordId)->update(['status' => $status, 'recored_at' => now()]);
	}

	public function HandleLedgerRecord(CollectDebtRequest $collectDebtRequest, int $readyRecordId)
	{
		return DB::transaction(function () use ($collectDebtRequest, $readyRecordId) {

			if ($collectDebtRequest->status_recored != CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO) {
				throw new Exception('request is processing record...');
			}

			$updateLenDangGhiSo = CollectDebtRequest::query()
				->where('id', $collectDebtRequest->id)
				->where('status_recored', CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO)
				->update([
					'status_recored' => CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO,
					'time_updated' => now()->timestamp
				]);

			if (!$updateLenDangGhiSo) {
				throw new Exception('loi khong the update len trang thai DANG GHI SO');
			}

			$collectDebtRequest = CollectDebtRequest::find($collectDebtRequest->id); // khong dung refresh

			if ($collectDebtRequest->status_recored != CollectDebtEnum::REQUEST_STT_RC_DANG_GHI_SO) {
				throw new Exception('ban ghi yeu cau dang khong o trang thai DANG GHI SO');
			}

			// cho ghi so vi khong co partner
			if ($collectDebtRequest->isYeuCauTrichLichThuQuaKhu()) {
				$yeuCauGhiSoThanhCong = $this->__thucHienGhiSo($collectDebtRequest, $readyRecordId);
				return $yeuCauGhiSoThanhCong;
			}

			// cho ghi so vi khong co partner
			if ($collectDebtRequest->isTrichTayGiamPhi()) {
				$yeuCauGhiSoThanhCong = $this->__thucHienGhiSo($collectDebtRequest, $readyRecordId);
				return $yeuCauGhiSoThanhCong;
			}


			$collectDebtPartner = CollectDebtPartner::query()->firstWhere([
				'payment_method_code' => $collectDebtRequest->payment_method_code,
				'partner_request_id' => $collectDebtRequest->partner_request_id,
			]);

			throw_if(!$collectDebtPartner, new Exception('Khong co thong tin partner cua yc'));

			// Kiem tra so tien tren partner va so tien thanh cong tren yeu cau
			if ($collectDebtPartner->amount_receiver != $collectDebtRequest->amount_receiver) {
				
				// if ($collectDebtPartner->payment_method_code == 'MPOS') {
				// 	// so tien trich thuc te
				// 	$soTienTrichTc = $collectDebtPartner->amount_receiver;
				// 	$collectDebtRequest->amount_receiver = $soTienTrichTc;
				// 	$collectDebtRequest->status_recored = CollectDebtEnum::REQUEST_STT_RC_CHUA_GHI_SO;
				// 	$result = $collectDebtRequest->save();

				// 	if (!$result) {
				// 		throw new Exception('So tien thanh cong tren yc bi khac voi partner');
				// 	}

				// 	return $collectDebtRequest;
				// }

				$msg = sprintf('env: %s --- ParnterId: %s --- RequestId: %s đang không khớp số tiền thành công', config('app.env'), $collectDebtPartner->id, $collectDebtRequest->partner_request_id);
				TelegramAlert::alertGhiSo($msg);
				throw new Exception($msg);
			}

			$yeuCauGhiSoThanhCong = $this->__thucHienGhiSo($collectDebtRequest, $readyRecordId);

			return $yeuCauGhiSoThanhCong;
		});
	}

	private function __thucHienGhiSo(CollectDebtRequest $collectDebtRequest, $readyRecordId)
	{
		$collectDebtLedger = app(TaoBanGhiLedgerSubAction::class)->run($collectDebtRequest);

		throw_if(!$collectDebtLedger, new Exception('khong the tao ban ghi ghi so.'));

		$collectDebtRequest->status_recored = CollectDebtEnum::REQUEST_STT_RC_DA_GHI_SO;
		$collectDebtRequest->time_recored = now()->timestamp;
		$collectDebtRequest->recored_by = Helper::getCronJobUser();
		$collectDebtRequest->time_completed = now()->timestamp;
		$collectDebtRequest->completed_by = Helper::getCronJobUser();
		$r = $collectDebtRequest->save();
		
		throw_if(!$r, new Exception('[THAT BAI] - khong the cap nhat trang thai la: DA GHI SO'));

		CollectDebtStatisticPartner::query()->where([
			'contract_code' => $collectDebtRequest->contract_code,
		])->update(['status_sync' => 0, 'time_sync' => now()->timestamp]);
		
		return $collectDebtRequest;
	}
}  // End class