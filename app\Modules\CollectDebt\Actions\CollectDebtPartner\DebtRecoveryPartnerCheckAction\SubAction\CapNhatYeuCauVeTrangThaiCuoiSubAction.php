<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction;

use Exception;
use App\Lib\Helper;
use Illuminate\Support\Str;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPartner;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\Task\CongTienTrichNoThanhCongSubAction;

class CapNhatYeuCauVeTrangThaiCuoiSubAction
{
  public function run(CollectDebtPartner $collectDebtPartner): array
  {
    $collectDebtRequestTheoPartner = CollectDebtRequest::where('partner_request_id', $collectDebtPartner->partner_request_id)->first();
    
		// Yeu cau ghi so roi thi khong can cap nhat gi them nua
    if ($collectDebtRequestTheoPartner->isRequestDaGhiSo()) {

			$collectDebtPartner->description = 'Chạy vào luồng đã ghi sổ?';
      $collectDebtPartner->status = CollectDebtEnum::PARTNER_STT_DA_XU_LY;
      $r = $collectDebtPartner->save();

			throw_if(!$r, new Exception('Không thể cập nhật partner về trạng thái đã xử lý'));

      return [
        'partner' => $collectDebtPartner,
        'is_excess' => 1,
      ];
    }


    $soTienDungDeThanhToan = $collectDebtPartner->amount_receiver; 
		
		$congTienResult = app(CongTienTrichNoThanhCongSubAction::class)->run(
			$collectDebtRequestTheoPartner, 
			$soTienDungDeThanhToan
		);
		
		if (!$congTienResult) {
			throw new Exception('Không thể cộng tiền cho yêu cầu');
		}

		
		$collectDebtPartner->number_perform = 99;
		$collectDebtPartner->status = CollectDebtEnum::PARTNER_STT_DA_XU_LY;
		$collectDebtPartner->amount_payment = $soTienDungDeThanhToan;

		$collectDebtPartner->time_updated = time();
		$collectDebtPartner->updated_by = Helper::getCronJobUser();

		$collectDebtPartner->time_complated = time();
		$collectDebtPartner->complated_by = Helper::getCronJobUser();

		$collectDebtPartner->time_created_request = time();
		$collectDebtPartner->created_request_by = Helper::getCronJobUser();

		$collectDebtPartner->time_processing = time();
		$collectDebtPartner->processing_by = Helper::getCronJobUser();
		
		$desc = $collectDebtPartner->description . ' --> ' . sprintf('Xử lý tiền về %s', Helper::priceFormat($collectDebtPartner->amount_payment));
		$collectDebtPartner->description = Str::limit($desc, 255);
		$savePartnerResult = $collectDebtPartner->save(); 
		
		if (!$savePartnerResult) {
			throw new Exception("PartnerId: $collectDebtPartner->id khong the saved ve trang thai cuoi cung");
		}

		$readyRecord = app(CreateReadyRecordSubAction::class)->run(
			$collectDebtPartner->partner_request_id, 
			$collectDebtPartner->contract_code
		);

		if (!$readyRecord || empty($readyRecord->id)) {
			throw new Exception("PartnerId: $collectDebtPartner->id khong the tao ready record");
		}

    return [
      'partner' => $collectDebtPartner,
      'is_excess' => 0,
    ];
  }
} // End class