<?php

namespace App\Lib;

use Exception;
use Throwable;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Http;

define('ACCOUTING_TOPIC', env('ACCOUTING_TOPIC_TELE', 4));              // topic hạch toán
define('TRICH_TAY_TOPIC', env('TRICH_TAY_TOPIC_TELE', 32));           // topic trích tay
define('PARNER_TOPIC', env('PARNER_TOPIC_TELE', 2));                    // topic partner check
define('NOTIFY_PARTNER', env('NOTIFY_PARTNER_TELE', 35));           // topic partner notify
define('CREATE_REQUEST_TOPIC', env('CREATE_REQUEST_TOPIC_TELE', 12));// topic tạo yêu cầu trích
define('SEND_REQUEST_TO_PARTNER_TOPIC', env('SEND_REQUEST_TO_PARTNER_TOPIC_TELE', 20));  // topic gửi yêu cầu sang hệ thống đối tác
define('GHI_SO_TOPIC', env('GHI_SO_TOPIC_TELE', 24));                // topic ghi sổ
define('CUT_OFF_TOPIC', env('CUT_OFF_TOPIC_TELE', 16));              // topic cut off
define('CANCEL_MPOS_TOPIC', env('CANCEL_MPOS_TOPIC_TELE', 38));     // topic cancel
define('DAY_CHI_DAN_TOPIC', env('DAY_CHI_DAN_TOPIC_TELE', 28));     // topic đẩy chỉ dẫn
define('HOANTIEN_TOPIC', env('HOANTIEN_TOPIC_TELE', 41));           // topic hoàn tiền
define('NEXTLEND_ALERT_GROUP', env('NEXTLEND_ALERT_GROUP_TELE', '-1002108888838'));           // topic hoàn tiền
define('MAIL_NHAC_NO_TOPC', env('NEXTLEND_ALERT_GROUP_TELE', '268803'));           // topic hoàn tiền
define('WAIT_PROCESS_TOPIC', env('WAIT_PROCESS_TOPIC_TELE', '310739'));           // topic wait process
define('CAC_LOAI_MAILS_TOPIC', env('CAC_LOAI_MAILS_TOPIC_TELE', '320456'));           // topic mails

class TelegramAlert
{
    const IS_SEND_MESSAGE = true;


    protected static $_url = 'https://api.telegram.org/bot';
    protected static $_token = '5773937211:AAErTeeICkrv3BWmxWfmpEl0F3C9oLjsqlY';

		public function checkConstants() {
			return [
				'ACCOUTING_TOPIC' => ACCOUTING_TOPIC,
				'TRICH_TAY_TOPIC' => TRICH_TAY_TOPIC,
				'PARNER_TOPIC' => PARNER_TOPIC,
				'NOTIFY_PARTNER' => NOTIFY_PARTNER,
				'CREATE_REQUEST_TOPIC' => CREATE_REQUEST_TOPIC,
				'SEND_REQUEST_TO_PARTNER_TOPIC' => SEND_REQUEST_TO_PARTNER_TOPIC,
				'GHI_SO_TOPIC' => GHI_SO_TOPIC,
				'CUT_OFF_TOPIC' => CUT_OFF_TOPIC,
				'CANCEL_MPOS_TOPIC' => CANCEL_MPOS_TOPIC,
				'DAY_CHI_DAN_TOPIC' => DAY_CHI_DAN_TOPIC,
				'HOANTIEN_TOPIC' => HOANTIEN_TOPIC,
				'NEXTLEND_ALERT_GROUP' => NEXTLEND_ALERT_GROUP,
				'IS_SEND_MESSAGE' => self::IS_SEND_MESSAGE
			];
		}

    public static function sendMessage($text, $type = 'Request', $topicId = 2)
    {
        if (!self::IS_SEND_MESSAGE) {
            return true;
        }
        
        if (!is_string($text)) {
            $detail = "\n Message: " . $text->getMessage();
            $detail .= "\n Line:" . $text->getLine();
            $detail .= "\n File: " . $text->getFile();
        } else {
            $detail = "\n {$type}: <pre>" . $text . "</pre>";
        }

        $text .= sprintf('%sURL: /%s --- ENV: %s', PHP_EOL,request()->path(), config('app.env'));

        try {
            return self::pushNotyDiscord($text, $type);
        } catch (Exception $e) {
            return false;
        }
    }

    public static function sendAccouting($text, $type = 'Request')
    {
        return self::sendMessage($text, $type, ACCOUTING_TOPIC);
    }

    public static function sendHoanTien($text, $type = 'Request')
    {
        return self::sendMessage($text, $type, HOANTIEN_TOPIC);
    }

    public static function sendCreateRequest($text, $type = 'Request')
    {
        return self::sendMessage($text, $type, CREATE_REQUEST_TOPIC);
    }

    public static function sendMessagePartner($text, $type = 'Request')
    {
        return self::sendMessage($text, 'partner_check');
    }

    public static function sendNotifyPartner($text, $type = 'Request')
    {
        return self::sendMessage($text, $type, NOTIFY_PARTNER);
    }

    public static function sendTrichTay($text, $type = 'Request')
    {
        return self::sendMessage($text, $type, TRICH_TAY_TOPIC);
    }

    public static function sendRqToPartner($text, $type = 'Request')
    {
			$botToken = 'https://discord.com/api/webhooks/1390509747790286929/P4nedR-ktFLi6pe9SHP56WaDozWVmy93xtlNrVgyNiUybelbieJCVl1AMykcpzmcZ_qm';
			return self::pushNotyDiscord(json_encode($text, JSON_UNESCAPED_UNICODE), '', $botToken);
    }

    public static function sendGhiSo($text, $type = 'Request')
    {
        return self::sendMessage($text, $type, GHI_SO_TOPIC);
    }

    public static function sendCutOff($text, $type = 'Request')
    {
        return self::sendMessage($text, $type, CUT_OFF_TOPIC);
    }

    public static function sendCancelMpos($text, $type = 'Request')
    {
        return self::sendMessage($text, $type, CANCEL_MPOS_TOPIC);
    }

    public static function sendDayChiDan($text, $type = 'Request')
    {
			$text['env'] = config('app.env');
			$botToken = 'https://discord.com/api/webhooks/1389418359522594846/TQcQLiAR8YqOxxFM9Y4KA6MPh7gfRjZt1q_4GSa6uSWXReXSxXNqpHUCDfdFazQykquz';
      return self::pushNotyDiscord(json_encode($text, JSON_UNESCAPED_UNICODE), '', $botToken);
    }

		public static function sendJobMailNhacNo($text, $type = 'Request')
    {
        return self::sendMessage($text, $type, MAIL_NHAC_NO_TOPC);
    }

		public static function sendWaitProcess($text, $type = 'Wait Processes')
    {
        return self::sendMessage($text, $type, WAIT_PROCESS_TOPIC);
    }

		public static function sendEmails($text, $type = 'Mails')
    {
        return self::sendMessage($text, 'mails');
    }

    public static function pushNoty(array $params = [], string $botToken = '')
    {
        try {
            if (!empty($botToken)) {
                $uri = self::$_url . $botToken . '/sendMessage?parse_mode=html';
            } else {
                $uri = self::$_url . self::$_token . '/sendMessage?parse_mode=html';
            }

            $option['verify'] = false;
            $option['form_params'] = $params;
            $option['http_errors'] = false;
            $client = new Client();
            $response = $client->request("POST", $uri, $option);

            return json_decode($response->getBody(), true);
        } catch (\Throwable $e) {
            mylog(['TeleError' => $e->getTrace()]);
            // dump($e->getMessage());
        }
    }

		public static function alertLenhTrichBiTreo($text) {
			return self::pushNotyDiscord($text, '', config('services.discord.alert_lenh_trich_bi_treo'));
    }

		public static function alertHachToan($text) {
			return self::pushNotyDiscord($text, '', config('services.discord.alert_hach_toan'));
    }

		public static function alertTienVe($text) {
			return self::pushNotyDiscord($text, '', config('services.discord.alert_tien_ve'));
    }

		public static function alertCancelMposKhiCoNguonTienVe($text) {
			return self::pushNotyDiscord($text, '', config('services.discord.alert_cancel_mpos_khi_co_nguon_tien_ve'));
    }

		public static function alertTatToan($text) {
			return self::pushNotyDiscord($text, '', config('services.discord.alert_tat_toan'));
    }

		public static function alertCutOff($text) {
			return self::pushNotyDiscord($text, '', config('services.discord.alert_cutoff'));
    }

		public static function alertGuiLenhTrich($text) {
			return self::pushNotyDiscord($text, '', config('services.discord.alert_gui_lenh_trich'));
    }

		public static function alertKiemTraTrichNgay($text) {
			return self::pushNotyDiscord($text, '', config('services.discord.alert_kiem_tra_trich_ngay'));
    }

		public static function alertGhiSo($text) {
			return self::pushNotyDiscord($text, '', config('services.discord.alert_ghi_so'));
    }

		public static function pushNotyDiscord($text, $type='', $url='')
		{
			if (empty($url)) {
				$type = 'mails';
				$url = config('services.discord.' . $type);
			}
		
			try {
				return Http::timeout(3)
				->retry(1, 500)
				->withHeaders([
					'Content-Type' => 'application/json',
				])
				
				->post($url, [
					'content' => sprintf('[%s] ---- %s', now()->toDateTimeString()	, $text)
				]);
			}catch(\Throwable $th) {
				// Lỗi vẫn return true để luồng chính không ảnh hưởng
				return true;
			}
		}
} // End class
