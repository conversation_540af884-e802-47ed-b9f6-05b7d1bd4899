<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction;

use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction\DebtRecoveryLedgerAccountingSummaryV2Action;

class AccountingSummaryBatchAction extends BatchProcessingAction
{
	public $timeout = 90;

	public function __construct()
	{
		ini_set('max_execution_time', $this->timeout);
	}

	public function run()
	{
		$ledgerIds = CollectDebtLedger::query()
																	->where('status', CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN)
																	->where('status_summary', CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT)
																	->limit(config('nextlend.BATCHING_LIMIT'))
																	->pluck('id')
																	->toArray();

		if ( empty($ledgerIds) ) {
			return ['msg' => 'No data ledger need to accounting summary'];
		}

		$ranges = array_chunk($ledgerIds, $this->batchSize);

		$this->processBatch($ranges);

		return ['msg' => 'ok done'];
	}

	
	private function processBatch($ranges)
	{
		$client = parent::createHttpClient($this->timeout - 10);

		$baseUrl = config('app.url');

		$requests = function () use ($ranges, $baseUrl) {
			foreach ($ranges as $i => $r) {
				$url = sprintf('%s/ProcessCalcSummary?ids=%s', $baseUrl, implode(',', $r));
				yield $i => new Request('GET', $url);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => $this->batchCount,
			'fulfilled' => function ($response, $r) {
				// $body = (string)$response->getBody();
			},
			'rejected' => function (\Throwable $reason, $r) {
				$msg = "[AccoutingSummary ---> failed: " . $reason->getMessage();
				Log::info($msg);
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);

		return ['msg' => 'all done'];
	}

	public function ProcessCalcSummary() {
		$ids = request()->get('ids');
		$ledgerIds = explode(',', $ids);

		foreach ($ledgerIds as $id) {
			try {
				$this->HandleAccoutingSummary($id);
			}catch(\Throwable $th) {
				
			}
		}

		return ['msg' => 'ok done'];
	}

	// $id --> id sổ
	public function HandleAccoutingSummary($id)
	{
		$collectDebtLedger = CollectDebtLedger::query()
			->where('id', $id)
			->where('status', CollectDebtEnum::LEDGER_STT_DA_HACH_TOAN)
			->where('status_summary', CollectDebtEnum::LEDGER_STT_ACTION_CHUA_CAP_NHAT)
			->first();

		if (!$collectDebtLedger) {
			return 'Ledger NotFound or invalid status';
		}

		return DB::transaction(function () use ($collectDebtLedger) {
			return app(DebtRecoveryLedgerAccountingSummaryV2Action::class)->run($collectDebtLedger);
		});
	}
}  // End class