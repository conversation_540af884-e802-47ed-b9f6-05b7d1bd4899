[2025-09-22 11:27:48] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L2","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L2","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":[],"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"TNEX","summary":{"fee_overdue":0,"fee_overdue_cycle":0,"total_amount_receiver":0,"total_amount_excess_revenue":0,"total_amount_paid":0,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":0,"is_over_cycle":0,"number_over_cycle":0,"partner_code":"","profile_id":0},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221127_xO2SkE"}} 
[2025-09-22 11:27:51] local.ERROR: Hợp đồng `MPOS-*************-L2` đã tồn tại trong hệ thống {"exception":"[object] (App\\Exceptions\\ContractCodeExistException(code: 409): Hợp đồng `MPOS-*************-L2` đã tồn tại trong hệ thống at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Requests\\v1\\CollectDebtGuide\\DebtRecoveryContractGuideCreateNoXauRequest.php:254)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(26): App\\Modules\\CollectDebt\\Requests\\v1\\CollectDebtGuide\\DebtRecoveryContractGuideCreateNoXauRequest->failedValidation(Object(Illuminate\\Validation\\Validator))
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1140): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\CollectDebt\\Requests\\v1\\CollectDebtGuide\\DebtRecoveryContractGuideCreateNoXauRequest), Object(Illuminate\\Foundation\\Application))
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1105): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\CollectDebt\\Requests\\v1\\CollectDebtGuide\\DebtRecoveryContractGuideCreateNoXauRequest), Array)
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1090): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Col...', Object(App\\Modules\\CollectDebt\\Requests\\v1\\CollectDebtGuide\\DebtRecoveryContractGuideCreateNoXauRequest))
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(711): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Col...', Object(App\\Modules\\CollectDebt\\Requests\\v1\\CollectDebtGuide\\DebtRecoveryContractGuideCreateNoXauRequest))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(796): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Col...', Array, true)
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(637): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Col...', Array)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(781): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Col...', Array)
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteDependencyResolverTrait.php(79): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Col...')
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteDependencyResolverTrait.php(48): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteDependencyResolverTrait.php(28): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(41): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\CollectDebt\\Controllers\\v1\\CollectDebtGuide\\CollectDebtGuideController), 'DebtRecoveryCon...')
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(239): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\CollectDebt\\Controllers\\v1\\CollectDebtGuide\\CollectDebtGuideController), 'DebtRecoveryCon...')
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(196): Illuminate\\Routing\\Route->runController()
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\StripHtmlTagMiddleware.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\StripHtmlTagMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\CheckTokenMiddleware.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckTokenMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 {main}
"} 
[2025-09-22 11:28:11] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L2","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L2","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":[],"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"TNEX","summary":{"fee_overdue":0,"fee_overdue_cycle":0,"total_amount_receiver":0,"total_amount_excess_revenue":0,"total_amount_paid":0,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":0,"is_over_cycle":0,"number_over_cycle":0,"partner_code":"","profile_id":0},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221128_XiRBkO"}} 
[2025-09-22 11:28:12] local.ERROR: Hợp đồng `MPOS-*************-L2` đã tồn tại trong hệ thống {"exception":"[object] (App\\Exceptions\\ContractCodeExistException(code: 409): Hợp đồng `MPOS-*************-L2` đã tồn tại trong hệ thống at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Requests\\v1\\CollectDebtGuide\\DebtRecoveryContractGuideCreateNoXauRequest.php:254)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ValidatesWhenResolvedTrait.php(26): App\\Modules\\CollectDebt\\Requests\\v1\\CollectDebtGuide\\DebtRecoveryContractGuideCreateNoXauRequest->failedValidation(Object(Illuminate\\Validation\\Validator))
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Providers\\FormRequestServiceProvider.php(30): Illuminate\\Foundation\\Http\\FormRequest->validateResolved()
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1140): Illuminate\\Foundation\\Providers\\FormRequestServiceProvider->Illuminate\\Foundation\\Providers\\{closure}(Object(App\\Modules\\CollectDebt\\Requests\\v1\\CollectDebtGuide\\DebtRecoveryContractGuideCreateNoXauRequest), Object(Illuminate\\Foundation\\Application))
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1105): Illuminate\\Container\\Container->fireCallbackArray(Object(App\\Modules\\CollectDebt\\Requests\\v1\\CollectDebtGuide\\DebtRecoveryContractGuideCreateNoXauRequest), Array)
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1090): Illuminate\\Container\\Container->fireAfterResolvingCallbacks('App\\\\Modules\\\\Col...', Object(App\\Modules\\CollectDebt\\Requests\\v1\\CollectDebtGuide\\DebtRecoveryContractGuideCreateNoXauRequest))
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(711): Illuminate\\Container\\Container->fireResolvingCallbacks('App\\\\Modules\\\\Col...', Object(App\\Modules\\CollectDebt\\Requests\\v1\\CollectDebtGuide\\DebtRecoveryContractGuideCreateNoXauRequest))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(796): Illuminate\\Container\\Container->resolve('App\\\\Modules\\\\Col...', Array, true)
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(637): Illuminate\\Foundation\\Application->resolve('App\\\\Modules\\\\Col...', Array)
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(781): Illuminate\\Container\\Container->make('App\\\\Modules\\\\Col...', Array)
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteDependencyResolverTrait.php(79): Illuminate\\Foundation\\Application->make('App\\\\Modules\\\\Col...')
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteDependencyResolverTrait.php(48): Illuminate\\Routing\\ControllerDispatcher->transformDependency(Object(ReflectionParameter), Array, Object(stdClass))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\RouteDependencyResolverTrait.php(28): Illuminate\\Routing\\ControllerDispatcher->resolveMethodDependencies(Array, Object(ReflectionMethod))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(41): Illuminate\\Routing\\ControllerDispatcher->resolveClassMethodDependencies(Array, Object(App\\Modules\\CollectDebt\\Controllers\\v1\\CollectDebtGuide\\CollectDebtGuideController), 'DebtRecoveryCon...')
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(239): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\CollectDebt\\Controllers\\v1\\CollectDebtGuide\\CollectDebtGuideController), 'DebtRecoveryCon...')
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(196): Illuminate\\Routing\\Route->runController()
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\StripHtmlTagMiddleware.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\StripHtmlTagMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\CheckTokenMiddleware.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckTokenMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 {main}
"} 
[2025-09-22 11:28:26] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L2","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L2","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":[],"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"TNEX","summary":{"fee_overdue":0,"fee_overdue_cycle":0,"total_amount_receiver":0,"total_amount_excess_revenue":0,"total_amount_paid":0,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":0,"is_over_cycle":0,"number_over_cycle":0,"partner_code":"","profile_id":0},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221128_yAbZ7D"}} 
[2025-09-22 11:29:00] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L2","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L2","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":[],"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":0,"fee_overdue_cycle":0,"total_amount_receiver":0,"total_amount_excess_revenue":0,"total_amount_paid":0,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":0,"is_over_cycle":0,"number_over_cycle":0,"partner_code":"NEXTLEND","profile_id":****************},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221129_O8Gl0S"}} 
[2025-09-22 11:29:45] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L2","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L2","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":[],"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":0,"fee_overdue_cycle":0,"total_amount_receiver":0,"total_amount_excess_revenue":0,"total_amount_paid":0,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":0,"number_over_cycle":0,"partner_code":"NEXTLEND","profile_id":****************},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221129_MMUscv"}} 
[2025-09-22 11:30:11] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L2","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L2","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":[],"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":0,"fee_overdue_cycle":0,"total_amount_receiver":0,"total_amount_excess_revenue":0,"total_amount_paid":0,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221130_PUNlpW"}} 
[2025-09-22 11:30:34] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L2","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L2","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":[],"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":0,"fee_overdue_cycle":0,"total_amount_receiver":0,"total_amount_excess_revenue":0,"total_amount_paid":0,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221130_c4blry"}} 
[2025-09-22 11:30:59] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L2","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L2","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":[],"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":0,"fee_overdue_cycle":0,"total_amount_receiver":0,"total_amount_excess_revenue":0,"total_amount_paid":0,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221130_BTN4rv"}} 
[2025-09-22 11:32:21] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L2","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L2","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":[[]],"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":0,"fee_overdue_cycle":0,"total_amount_receiver":0,"total_amount_excess_revenue":0,"total_amount_paid":0,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221132_alF6wx"}} 
[2025-09-22 11:38:32] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L2","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L2","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":{"contract":{"15":{"id":15,"name":"NhÃ¢n viÃªn tháº©m Ä‘á»‹nh","code":"NV_THAM_DINH","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}},"merchant":{"14":{"id":14,"name":"Telesales","code":"NV_TELE_SALE","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}}},"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":0,"fee_overdue_cycle":0,"total_amount_receiver":0,"total_amount_excess_revenue":0,"total_amount_paid":0,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221138_QnCBXm"}} 
[2025-09-22 11:39:02] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L2","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L2","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":{"contract":{"15":{"id":15,"name":"NhÃ¢n viÃªn tháº©m Ä‘á»‹nh","code":"NV_THAM_DINH","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}},"merchant":{"14":{"id":14,"name":"Telesales","code":"NV_TELE_SALE","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}}},"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":100000,"fee_overdue_cycle":0,"total_amount_receiver":200000,"total_amount_excess_revenue":0,"total_amount_paid":200000,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221139_2ZgibP"}} 
[2025-09-22 11:39:48] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L2","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L2","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":{"contract":{"15":{"id":15,"name":"NhÃ¢n viÃªn tháº©m Ä‘á»‹nh","code":"NV_THAM_DINH","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}},"merchant":{"14":{"id":14,"name":"Telesales","code":"NV_TELE_SALE","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}}},"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":100000,"fee_overdue_cycle":0,"total_amount_receiver":200000,"total_amount_excess_revenue":0,"total_amount_paid":200000,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221139_LtePdt"}} 
[2025-09-22 11:39:48] local.ERROR: Hợp đồng `MPOS-*************-L2` đã tồn tại trong hệ thống {"exception":"[object] (Exception(code: 0): Hợp đồng `MPOS-*************-L2` đã tồn tại trong hệ thống at C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Modules\\CollectDebt\\Controllers\\v1\\CollectDebtGuide\\CollectDebtGuideController.php:115)
[stacktrace]
#0 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Modules\\CollectDebt\\Controllers\\v1\\CollectDebtGuide\\CollectDebtGuideController->DebtRecoveryContractGuideCreateNoXau(Object(App\\Modules\\CollectDebt\\Requests\\v1\\CollectDebtGuide\\DebtRecoveryContractGuideCreateNoXauRequest))
#1 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('DebtRecoveryCon...', Array)
#2 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(239): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Modules\\CollectDebt\\Controllers\\v1\\CollectDebtGuide\\CollectDebtGuideController), 'DebtRecoveryCon...')
#3 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(196): Illuminate\\Routing\\Route->runController()
#4 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(685): Illuminate\\Routing\\Route->run()
#5 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\StripHtmlTagMiddleware.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\StripHtmlTagMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\CheckTokenMiddleware.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckTokenMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(687): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#12 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#13 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(628): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#14 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(617): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#15 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(165): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#16 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\LoggingMiddleware.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\LoggingMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\laragon8\\www\
extpay-web\\api-request-debt\\app\\Http\\Middleware\\AppendApiRequestIdMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AppendApiRequestIdMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(140): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 C:\\laragon8\\www\
extpay-web\\api-request-debt\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(109): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 C:\\laragon8\\www\
extpay-web\\api-request-debt\\public\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-09-22 12:09:09] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L21","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L2","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":{"contract":{"15":{"id":15,"name":"NhÃ¢n viÃªn tháº©m Ä‘á»‹nh","code":"NV_THAM_DINH","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}},"merchant":{"14":{"id":14,"name":"Telesales","code":"NV_TELE_SALE","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}}},"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":100000,"fee_overdue_cycle":0,"total_amount_receiver":200000,"total_amount_paid":200000,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************,"contract_code":"MPOS-*************-L2"},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221209_QRTys4"}} 
[2025-09-22 12:10:01] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L21","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L2","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":{"contract":{"15":{"id":15,"name":"NhÃ¢n viÃªn tháº©m Ä‘á»‹nh","code":"NV_THAM_DINH","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}},"merchant":{"14":{"id":14,"name":"Telesales","code":"NV_TELE_SALE","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}}},"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":100000,"fee_overdue_cycle":0,"total_amount_receiver":200000,"total_amount_paid":200000,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************,"contract_code":"MPOS-*************-L2"},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221210_qZIkr5"}} 
[2025-09-22 12:10:19] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L21","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L21","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":{"contract":{"15":{"id":15,"name":"NhÃ¢n viÃªn tháº©m Ä‘á»‹nh","code":"NV_THAM_DINH","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}},"merchant":{"14":{"id":14,"name":"Telesales","code":"NV_TELE_SALE","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}}},"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":100000,"fee_overdue_cycle":0,"total_amount_receiver":200000,"total_amount_paid":200000,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************,"contract_code":"MPOS-*************-L2"},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221210_FjEYTi"}} 
[2025-09-22 12:10:31] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L21","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L21","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":{"contract":{"15":{"id":15,"name":"NhÃ¢n viÃªn tháº©m Ä‘á»‹nh","code":"NV_THAM_DINH","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}},"merchant":{"14":{"id":14,"name":"Telesales","code":"NV_TELE_SALE","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}}},"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":100000,"fee_overdue_cycle":0,"total_amount_receiver":200000,"total_amount_paid":200000,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************,"contract_code":"MPOS-*************-L21"},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221210_2JJUeU"}} 
[2025-09-22 12:10:56] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L21","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L21","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":{"contract":{"15":{"id":15,"name":"NhÃ¢n viÃªn tháº©m Ä‘á»‹nh","code":"NV_THAM_DINH","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}},"merchant":{"14":{"id":14,"name":"Telesales","code":"NV_TELE_SALE","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}}},"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":100000,"fee_overdue_cycle":0,"total_amount_receiver":200000,"total_amount_paid":200000,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************,"contract_code":"MPOS-*************-L21"},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221210_1LKL8j"}} 
[2025-09-22 13:07:12] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L29","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L29","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":{"contract":{"15":{"id":15,"name":"NhÃ¢n viÃªn tháº©m Ä‘á»‹nh","code":"NV_THAM_DINH","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}},"merchant":{"14":{"id":14,"name":"Telesales","code":"NV_TELE_SALE","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}}},"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":100000,"fee_overdue_cycle":0,"total_amount_receiver":200000,"total_amount_paid":200000,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************,"contract_code":"MPOS-*************-L29"},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221307_hpQDGy"}} 
[2025-09-22 13:07:13] local.INFO: [NextlendCore][GenerateQRCode_create] ----> {"func":"GenerateQRCode_create","inputRaw":{"qrcode_string":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2"},"inputHash":{"func":"GenerateQRCode_create","checksum":"bd1f6b50972cc4be1cef0e562a05b579","params":"gcGolNVGs5pivbSfNPlwgVDp/ss8/N8SyI/bBrqpaAnMQuLL4mL8bY7iln2KeS01o+9CvHfqC6/JG80aLcBahGQoyRLfVx3dHqdbM0cVfB20lgVJjSgjiTmYGAq+sxMtYNJ8zNdJcUCUcZKI4Zs91J0HXEZPQX9TgMnllttpWmglGV7tumvJXNAEd3TK6pKH8tihluUByGJVloXWZKfqsPNM6eg1FCGoAjKMIhP6wwkTuqqm8gDJUIS19vI6dFn4gF6fNVDgiIW06JRO2ZnS/g==","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"},"result":{"errorCode":"success","errorDescription":"Thành công","data":"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","remote_ip":"127.0.0.1","language":"vi","function":"GenerateQRCode_create","checksum":"24e7308bcba81b001b51c5a79081b1c6"}} 
[2025-09-22 13:07:21] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L29","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L29","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":{"contract":{"15":{"id":15,"name":"NhÃ¢n viÃªn tháº©m Ä‘á»‹nh","code":"NV_THAM_DINH","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}},"merchant":{"14":{"id":14,"name":"Telesales","code":"NV_TELE_SALE","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}}},"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":100000,"fee_overdue_cycle":0,"total_amount_receiver":200000,"total_amount_paid":200000,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************,"contract_code":"MPOS-*************-L29"},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221307_DJAkbv"}} 
[2025-09-22 13:07:22] local.INFO: [NextlendCore][GenerateQRCode_create] ----> {"func":"GenerateQRCode_create","inputRaw":{"qrcode_string":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2"},"inputHash":{"func":"GenerateQRCode_create","checksum":"8118a2490d569200da0f491fc28c0e0a","params":"oxAf/CdCx/KHNMEwlWu5+0751gWohHhypC1+9WLNu5NyYfYLM3TcrGszumQ2TJmF+/V69leC0tNsC02E2olfff5FIlkjQmVjVVeUZuJrUb3ynSLJ6Nnuw7k/AtA7lIp+e8so0Jn+K1iOdTmno+favOVE9HcIhFn4Pw+LtCQKJQ5rYc2MjzupyEvU8IFz5tt7kprILWKkpLM0ivtCWdP5wJmf51P973yB0+B79D7qg8gb5FGZRTxS9rKO2/sKJWAUegjtQcKxWBGMS1wRScKj+Q==","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"},"result":{"errorCode":"success","errorDescription":"Thành công","data":"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","remote_ip":"127.0.0.1","language":"vi","function":"GenerateQRCode_create","checksum":"ca422169dd04d50555978d699b6c9616"}} 
[2025-09-22 13:08:50] local.INFO: DebtRecoveryContractGuideGetByContractCode {"request":{"data":{"contract_code":"MPOS-*************-L29","fields":[]},"checksum":"a255652b13cdb7410b3392465bdd36fd","channel_code":"WEBPARTNER","time_request":1732875187,"version":"1.0","api_request_id":"DEBT_09221308_OoA9mq"}} 
[2025-09-22 13:13:23] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L29","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L29","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":{"contract":{"15":{"id":15,"name":"NhÃ¢n viÃªn tháº©m Ä‘á»‹nh","code":"NV_THAM_DINH","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}},"merchant":{"14":{"id":14,"name":"Telesales","code":"NV_TELE_SALE","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}}},"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":100000,"fee_overdue_cycle":0,"total_amount_receiver":200000,"total_amount_paid":200000,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************,"contract_code":"MPOS-*************-L29"},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221313_Ik804w"}} 
[2025-09-22 13:13:24] local.INFO: [NextlendCore][GenerateQRCode_create] ----> {"func":"GenerateQRCode_create","inputRaw":{"qrcode_string":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2"},"inputHash":{"func":"GenerateQRCode_create","checksum":"1ac42326287e45b82e76a63c3347529a","params":"XFb4pV2r/WSW/RxHKylaCDhQbmKehv4vWf+rLp4HR7RnJM8tceUuCrtBYxYe2GBmPd97W7jlizSdF+ty8l2NYA5XU5Ciuk0kdM9t0Huje79GlGLNKLxu31+6gWp6TtZKWisosGIzEFBOJJVg3fTQBmjx1ONaBKnCu2tYvy8y52AvrQ1wytGuTH3R/WlyvAYKlLc55HgBRtEcfsz4KXXyXEWb/nZwWCkX82qdfJqeLHsjkwmjvDDsVS+EG4343oJ42v0jwmX43z6NLcdf3ncCiQ==","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"},"result":{"errorCode":"success","errorDescription":"Thành công","data":"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","remote_ip":"127.0.0.1","language":"vi","function":"GenerateQRCode_create","checksum":"b7ac52feb1afa42ac41dd25ea01e8599"}} 
[2025-09-22 13:14:09] local.INFO: CreateRequestAutoBatchAction {"request":{"api_request_id":"DEBT_09221314_EsUKvY"}} 
[2025-09-22 13:14:49] local.INFO: CreateRequestAutoBatchAction {"request":{"api_request_id":"DEBT_09221314_H1FgLT"}} 
[2025-09-22 13:16:30] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L29","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L29","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":{"contract":{"15":{"id":15,"name":"NhÃ¢n viÃªn tháº©m Ä‘á»‹nh","code":"NV_THAM_DINH","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}},"merchant":{"14":{"id":14,"name":"Telesales","code":"NV_TELE_SALE","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}}},"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":100000,"fee_overdue_cycle":0,"total_amount_receiver":200000,"total_amount_paid":200000,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************,"contract_code":"MPOS-*************-L29"},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221316_aZ6jvl"}} 
[2025-09-22 13:16:30] local.INFO: [NextlendCore][GenerateQRCode_create] ----> {"func":"GenerateQRCode_create","inputRaw":{"qrcode_string":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2"},"inputHash":{"func":"GenerateQRCode_create","checksum":"228f0f9803c97373ae51af30784ac8ea","params":"sBJVpa0AnZ6MgzEmRqd99ZRCve4TOviqG9TNM40xLPfjqUxMJgLNuqyX/IkeGU1evEfUJzASdTTg3tfKfjH7GDX//fz80l/41Ar36rWZgljkSlmRD42DvEEfnjbA6kv7/KjchkCg4osn0cDLPxrwzw0gPEVhpPsClAZRWbrJ7dZLpUcxp8CfrL9b6zae7pYHoRXW8hnmbqx08kEEVm4HhG5L8xLf5XsrRztJyUsrWW2wT1p3a25LyC+4TFa2rTvL9UUkJk4qKU7lusy6XAtVUA==","username_request":"guest","users_request_id":"1","language":"vi","client_ip":"127.0.0.1"},"result":{"errorCode":"success","errorDescription":"Thành công","data":"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","remote_ip":"127.0.0.1","language":"vi","function":"GenerateQRCode_create","checksum":"05cd4a7d74b0fc77fda4a6293b19e7ae"}} 
[2025-09-22 13:22:54] local.INFO: DebtRecoveryContractGuideCreateNoXau {"request":{"data":{"action_code":"CREATE_GUIDE","profile_id":"****************","contract_code":"MPOS-*************-L29","contract_cycle":10,"contract_type":1,"contract_intervals":1,"contract_time_start":"20-06-2025 00:00:00","contract_time_end":"30-06-2025 23:59:58","amount":********,"description":" V4","payment_guide":[{"payment_method_code":"MPOS","payment_channel_code":"MPOS","payment_account_id":"********","other_data":{"payment_account_name":"TRAN THI HOAI THANH","payment_account_number":"********","payment_account_branch":"NA","payment_account_bank_code":"NA"}},{"payment_method_code":"IB_OFF","payment_channel_code":"BANK","payment_account_id":"*************","other_data":{"payment_account_name":"Tran Thị Hoai Thanh","payment_account_number":"*************","payment_account_branch":"Hoàn Kiếm","payment_account_bank_code":"ACB"}},{"payment_method_code":"VIRTUALACCOUNT","payment_channel_code":"PAYON","payment_account_id":"M828S34556622867708","other_data":{"payment_account_name":"Công ty Cổ phần Công nghệ Vi Mô","payment_account_number":"M828S34556622867708","payment_account_branch":"NA","payment_account_bank_code":"BIDV","qrCode":"00020101021138630010A000000727013300069704180119962NPS06009521923660208QRIBFTTA53037045802VN62270823MCQ7CK7VVNAKRNP TK UTMC630442c2","qrImage":""}}],"list_fee":[{"type":1,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":2,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":3,"percent_fee":0.205479,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0.205479,"flat_fee":0}},{"type":4,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":8,"percent_fee":1,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":1,"flat_fee":0}},{"type":9,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":[]},{"type":11,"percent_fee":0,"flat_fee":0,"fee_max":0,"fee_min":0,"other_data":{"percent_fee":0,"flat_fee":0}}],"contract_data":{"id":875,"type":1,"contract_service_code":"VAY","account_disbursement_type":1,"source":4,"contract_id":null,"partner_id":1,"merchant_id":664,"profile_id":****************,"borrower_id":680,"borrower_bank_id":11136,"investors_id":0,"contract_package_id":55,"number":2,"cycle":10,"intervals":1,"time_start":**********,"time_end":**********,"amount":********,"amount_fee_evaluation":100000,"currency":"VND","contract_fee_policy_id":0,"flat_fee":0,"percent_fee":1,"renew_percent_fee":0,"renew_flat_fee":0,"overdue_percent_fee":0.205479,"overdue_flat_fee":0,"fee_data":"{\"contract_fee\":{\"contract_fee_id\":\"**************\",\"package_detail_id\":\"1538\",\"percent_fee\":\"1\",\"flat_fee\":\"0\",\"overdue_flat_fee\":\"0\",\"overdue_percent_fee\":\"0.205479\",\"flat_fee_cycle\":\"0\",\"percent_fee_cycle\":\"1\"},\"promotion_data\":{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"},\"contract_fee_incentive\":[],\"amount_fee_evaluation\":{\"percent_fee\":\"1\",\"flat_fee\":0},\"contract_fee_evaluation_incentive\":[],\"package_name\":null,\"penalty_flat_fee\":0,\"penalty_percent_fee\":0,\"contract_not_fee_intervals\":[]}","fee_data_bak":null,"status":6,"status_send_partner":1,"status_query":1,"status_refund":1,"status_partner":1,"status_renew":2,"status_debt":1,"status_bad_debt":2,"status_overdue":2,"status_overdue_period":2,"status_convert_contract_account":1,"status_create_contract_fee_value":1,"status_convert_borrower_debit":1,"finished":1,"deferred":1,"fee_deferred":2,"promotion_id":null,"promotion_data":"{\"promotion_refund\":\"\",\"promotion_donate_tingbox\":\"\",\"promotion_refund_auto_debt_recovery\":\"\",\"promotion_refund_settlement\":\"\"}","urlfile":null,"linkfile":null,"content":"Tạo hợp đồng","url_content":null,"his_start_deduction":0,"number_day_overdue":365,"number_day_overdue_free":0,"number_day_status_debt":11,"next_payment_period":0,"pay_number_period_overdue":0,"pay_number_day_overdue":0,"report_data":null,"avg_month_count":0,"avg_trans_value":0,"avg_trans_number":0,"reject_reason":null,"active_reason":"NEXTPAY_6666_51529_0101.02.007240_TNF giải ngân cho KH _Đoàn Thị Dung_ngày 2025-06-20","number_rum_promotion":0,"percent_refund":0,"amount_applied_refund":0,"amount_refund":0,"merchant_belong_group":1,"renew_type":1,"contract_renewed_amount":0,"amount_fixed_refund":0,"number_get_deferred":0,"created_admin_user_id":1,"updated_admin_user_id":4,"verified_admin_user_id":1,"rejected_admin_user_id":0,"setdayoverduefree_admin_user_id":0,"version":3,"contract_past_number_day":0,"mobilecheck_finished_rejected":null,"converted_data":0,"number_submit_document":1,"location":null,"number_get_update":0,"time_queried":null,"time_created":1750298773,"time_verified":1750387142,"time_rejected":null,"time_completed":1750387264,"time_setdayoverduefree":null,"time_finished":null,"time_renewed":null,"time_updated":1750405565,"number_run":0,"user_admin_id":1,"merchant_verify_email":3,"time_verify_email":null,"signed":2,"status_created_guide":5,"convert_v2":1,"users_admin":{"mobile":"0356879112","fullname":"Autocashout1","email":"<EMAIL>"},"contract_code":"MPOS-*************-L29","contract_fee":200000,"contract_receiver":9800000,"time_start_as_date":"20-06-2025 00:00:00","time_end_as_date":"30-06-2025 23:59:58","user_action":{"contract":{"15":{"id":15,"name":"NhÃ¢n viÃªn tháº©m Ä‘á»‹nh","code":"NV_THAM_DINH","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}},"merchant":{"14":{"id":14,"name":"Telesales","code":"NV_TELE_SALE","user":[{"id":"Over 9 levels deep, aborting normalization","username":"Over 9 levels deep, aborting normalization","email":"Over 9 levels deep, aborting normalization","mobile":"Over 9 levels deep, aborting normalization","fullname":"Over 9 levels deep, aborting normalization","address":"Over 9 levels deep, aborting normalization"}]}}},"contract_partner":{"registrationId":"d9966f4f34139801856f917fa4320f71","idNo":"0101.02.007240","signedTime":"2025-06-20T09:38:38.669418501","contractRequestId":"250620093619943023","partner_code":"TNEX"}},"profile_data":{"id":"****************","partner":{"id":1,"type":1,"code":"MPOS","name":"MPOS VIỆT NAM","field_check":"id","email":"<EMAIL> ","mobile":"0911111111","hotline":"0311111111","status":1,"created_by":null,"updated_by":null,"time_created":1492049818,"time_updated":1492049867},"merchant":{"id":664,"contract_past_number_day":0,"partner_merchant_code":"********","profile_id":****************,"partner_payment_code":null,"partner_id":1,"cid":null,"source":0,"time_partner_actived":1590055969,"fullname":"TRAN THI HOAI THANH","short_name":"MC360_0985159156","average_revenue":0,"transaction_count_per_month":0,"business_representative":"TRAN THI HOAI THANH","passport":"024112666543","issue_date":"19-05-2025","issued_by":"Sở Kế hoạch và Đầu tư","certificate_id":null,"business_type_id":3,"business_areas_id":1,"email":"<EMAIL>","mobile":"***********","phone":null,"address":"Địa chỉ 1","address1":"Hà Nội","address2":"Quận Hai Bà Trưng","business_address":null,"business_chain":2,"city_id":11871,"district_id":11937,"operation_year":0,"description":null,"advance_number":2,"checksum":"874b0dbf938923d2787189ca9fd861db","wait_active":2,"sale_id":null,"sale_email":null,"users_admin_id":0,"status_action":1,"status":2,"status_care":1,"status_create_profile":1,"avg_month_count":12,"avg_trans_value":2000000000,"avg_trans_number":100,"evaluation_month":0,"evaluate_min":0,"evaluate_max":0,"loan_money":********,"evaluate_from_time":null,"evaluate_to_time":null,"belong_group":1,"belong_group_new":0,"amount_from":0,"amount_to":0,"condition_valid":2,"detail_trans":null,"detail_fee":null,"number_updated":0,"updated_admin_user_id":0,"created_admin_user_id":0,"locked_admin_user_id":0,"unlocked_admin_user_id":0,"actived_admin_user_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_handover":null,"time_updated_status_action":null,"time_created":1750133973,"time_updated":1750405565,"converted_data":0,"contract_user_admin_id":null,"deleted_user_admin_id":null,"sale_region_id":null,"sync":0,"sync_mynextpay":1,"longitude":"0","latitude":"0","time_set_profiled":1750298775,"time_deleted":null},"borrower":{"id":680,"partner_id":1,"merchant_id":664,"profile_id":****************,"fullname":"TRAN THI HOAI THANH","birthday":0,"id_verified":null,"issue_date":null,"issued_by":null,"email":"<EMAIL>","phone":null,"mobile":"84985159156","address":"Địa chỉ 1","checksum":"874b0dbf938923d2787189ca9fd861db","description":null,"number_updated":0,"status":2,"number_set_profile_id":0,"time_locked":null,"time_unlocked":null,"time_actived":null,"time_created":1750133973,"time_updated":null}},"created_by":{"id":1,"username":"autocashout","email":"<EMAIL>","fullname":"Autocashout1","mobile":"0356879112"},"other_data":"{\"company\":{\"type\":\"OTHER\",\"data\":{\"company_fullname\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_subname\":\"C\\u00f4ng ty Vi M\\u00f4\",\"company_code\":\"VIMO\",\"company_phone_number_1\":\"************\",\"company_phone_number_2\":\"\",\"company_email_1\":\"<EMAIL>\",\"company_email_2\":\"\",\"company_url_1\":\"https:\\/\\/mpos.vn\",\"company_url_2\":\"https:\\/\\/nextlend.vn\",\"company_address_1\":\"Tang 12A, Toa nha VTC Online, 18 Tam Trinh, Q. Hai Ba Trung, Ha Noi, Vietnam\",\"company_address_2\":\"\",\"company_product_code_1\":\"NEXTLEND\",\"company_product_group_1\":\"Next360\",\"company_bank_account_1\":\"************\",\"company_bank_holder_1\":\"C\\u00f4ng ty C\\u1ed5 ph\\u1ea7n C\\u00f4ng ngh\\u1ec7 Vi M\\u00f4\",\"company_bank_name_1\":\"Ng\\u00e2n h\\u00e0ng TMCP C\\u00f4ng Th\\u01b0\\u01a1ng Vi\\u1ec7t Nam (Vietinbank) \\u2013 Chi nh\\u00e1nh Ho\\u00e0ng Mai\"},\"time_modified\":**********,\"note\":\"\"},\"total_account_payment\":{\"amount_debit\":********,\"amount_refund\":0,\"fee_deferred\":0,\"fee_cycle_deferred\":0,\"discount_fee_deferred\":0,\"discount_fee_cycle_deferred\":0},\"overdue_cycle\":[]}","partner_code":"NEXTLEND","summary":{"fee_overdue":100000,"fee_overdue_cycle":0,"total_amount_receiver":200000,"total_amount_paid":200000,"total_fee_paid":0,"fee_overdue_paid":0,"fee_overdue_cycle_paid":0,"is_overdue":1,"is_over_cycle":2,"number_over_cycle":0,"number_day_overdue":15,"partner_code":"NEXTLEND","profile_id":****************,"contract_code":"MPOS-*************-L29"},"plans_today":{"total_root_debt":********,"total_overdue_fee":10000,"total_overcycle_fee":0,"amount_period_debit":300000}},"checksum":"a1e029dd20fe8ec0e4e683c2b53f6809","channel_code":"WEBPARTNER","time_request":**********,"version":"1.0","api_request_id":"DEBT_09221322_x3GdID"}} 
