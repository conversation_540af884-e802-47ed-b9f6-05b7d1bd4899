<?php

namespace App\Modules\CollectDebt\Controllers\v1\CollectDebtPartner;

use App\Lib\Helper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Modules\CollectDebt\Controllers\Controller;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Resources\CollectDebtPartnerResourceCollection;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerCreateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerCreateBatchRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerUpdateRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerGetByIdRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerSetStatusRequest;
use App\Modules\CollectDebt\Requests\v1\CollectDebtPartner\DebtRecoveryPartnerGetByPartnerTransactionIdRequest;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCreateAction\DebtRecoveryPartnerCreateAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerUpdateAction\DebtRecoveryPartnerUpdateAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerGetByIdAction\DebtRecoveryPartnerGetByIdAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\DebtRecoveryPartnerCheckImproveAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerSetStatusAction\DebtRecoveryPartnerSetStatusAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerSearchDataAction\DebtRecoveryPartnerSearchDataAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerGetByContractCodeAction\GetListPartnerByContractCodeAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\GetListRefundTransactionOnPartnerAction\GetListRefundTransactionOnPartnerAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerGetByContractCodeAction\DebtRecoveryPartnerGetByContractCodeAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerGetByPartnerTransactionIdAction\DebtRecoveryPartnerGetByPartnerTransactionIdAction;

class CollectDebtPartnerController extends Controller
{
	public function create(DebtRecoveryPartnerCreateRequest $request)
	{
		try {
			$collectDebtPartner = app(DebtRecoveryPartnerCreateAction::class)->run($request);
			$collectDebtRequest = CollectDebtRequest::query()->where('partner_request_id', $request->json('data.partner_request_id'))->first();

			$returnData = $collectDebtPartner->toArray();
			$returnData['request_id'] =  $collectDebtRequest->id ;
			
			return $this->successResponse($returnData, $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	} 

	public function createBatch(DebtRecoveryPartnerCreateBatchRequest $request)
	{
		if (app()->environment('production')) {
			return $this->errorResponse(400, 'Chỉ được phép chạy trên môi trường dev');
		}

		if (!config('app.debug')) {
			return $this->errorResponse(400, 'Chỉ được phép chạy khi debug = true');
		}

		try {
			$results = [];
			$errors = [];

			foreach ($request->json('data') as $index => $item) {
				try {
					// Tạo request object cho từng item
					$singleRequest = new DebtRecoveryPartnerCreateRequest();
					$singleRequest->setJson(new \Symfony\Component\HttpFoundation\ParameterBag([
						'data' => $item
					]));

					// Validate single request
					$validator = Validator::make(['data' => $item], $singleRequest->rules());
					if ($validator->fails()) {
						$errors[] = [
							'index' => $index,
							'partner_request_id' => $item['partner_request_id'] ?? 'N/A',
							'errors' => $validator->errors()->toArray()
						];
						continue;
					}

					// Xử lý tạo partner debt
					$collectDebtPartner = app(DebtRecoveryPartnerCreateAction::class)->run($singleRequest);
					$collectDebtRequest = CollectDebtRequest::query()->where('partner_request_id', $item['partner_request_id'])->first();

					$returnData = $collectDebtPartner->toArray();
					$returnData['request_id'] = $collectDebtRequest ? $collectDebtRequest->id : null;

					$results[] = [
						'index' => $index,
						'partner_request_id' => $item['partner_request_id'],
						'status' => 'success',
						'data' => $returnData
					];

				} catch (\Throwable $itemError) {
					$errors[] = [
						'index' => $index,
						'partner_request_id' => $item['partner_request_id'] ?? 'N/A',
						'error' => Helper::traceError($itemError)
					];
				}
			}

			$response = [
				'total_processed' => count($request->json('data')),
				'success_count' => count($results),
				'error_count' => count($errors),
				'results' => $results,
				'errors' => $errors
			];

			return $this->successResponse($response, $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function update(DebtRecoveryPartnerUpdateRequest $request)
	{
		try {
			$collectDebtPartner = app(DebtRecoveryPartnerUpdateAction::class)->run($request);
			return $this->successResponse($collectDebtPartner->toArray(), $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	} 

	public function setStatus(DebtRecoveryPartnerSetStatusRequest $request)
	{
		try {
			$collectDebtPartner = app(DebtRecoveryPartnerSetStatusAction::class)->run($request);
			return $this->successResponse($collectDebtPartner->toArray(), $request, 200, $collectDebtPartner->__apiMessage);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	} 


	public function getById(DebtRecoveryPartnerGetByIdRequest $request)
	{
		try {
			$collectDebtPartner = app(DebtRecoveryPartnerGetByIdAction::class)->run($request);
			return $this->successResponse($collectDebtPartner->toArray(), $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function searchData(Request $request)
	{
		try {
			$collectDebtPartnerPaginate = app(DebtRecoveryPartnerSearchDataAction::class)->run($request);
			$collectDebtPartnerResource = new CollectDebtPartnerResourceCollection($collectDebtPartnerPaginate);
			$response = $collectDebtPartnerResource->toResponse($request)->getData(true);
			return $this->successResponse($response, $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	} 


	public function getByPartnerTransactionId(DebtRecoveryPartnerGetByPartnerTransactionIdRequest $request)
	{
		try {
			$collectDebtPartner = app(DebtRecoveryPartnerGetByPartnerTransactionIdAction::class)->run($request);
			return $this->successResponse($collectDebtPartner->toArray(), $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function getByContractCode(Request $request) {
		try {
			$collectDebtPartners = app(DebtRecoveryPartnerGetByContractCodeAction::class)->run($request);
			$collectDebtPartnerResource = new CollectDebtPartnerResourceCollection($collectDebtPartners);
			$response = $collectDebtPartnerResource->toResponse($request)->getData(true);
			return $this->successResponse($response, $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function GetListPartnerByContractCode(Request $request) {
		try {
			$collectDebtPartners = app(GetListPartnerByContractCodeAction::class)->run($request);
			$collectDebtPartnerResource = new CollectDebtPartnerResourceCollection($collectDebtPartners);
			$response = $collectDebtPartnerResource->toResponse($request)->getData(true);
			return $this->successResponse($response, $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}

	public function getListRefundTransactionOnPartner(Request $request) {
		try {
			$partners = app(GetListRefundTransactionOnPartnerAction::class)->run($request);
			return $this->successResponse([
				'rows' => $partners->count(),
				'data' => $partners->toArray()
			], $request);
		}catch (\Throwable $th) {
			return $this->errorResponse($th->getCode(), Helper::traceError($th));
		}
	}
} // End class
