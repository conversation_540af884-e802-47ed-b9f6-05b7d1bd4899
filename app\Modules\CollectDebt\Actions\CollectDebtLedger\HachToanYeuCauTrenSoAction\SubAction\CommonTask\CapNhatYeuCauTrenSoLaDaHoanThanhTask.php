<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\SubAction\CommonTask;

use Exception;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtLedger;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;
use App\Modules\CollectDebt\Model\CollectDebtReadyRecord;
use App\Modules\CollectDebt\Model\CollectDebtContractLevel;

class CapNhatYeuCauTrenSoLaDaHoanThanhTask
{
	/**
	 * KHÔNG ĐƯỢC update by model, vì có object tự thêm vào -> nó sẽ gây lỗi
	 * 1. Cập nhật yêu cầu về trạng thái Đã hoàn thành
	 * 2. <PERSON><PERSON><PERSON> dấu lịch là đã gạch (đã hoàn thành) theo ngữ cảnh phù hợp
	 */
	public function run(CollectDebtLedger $ledger, array $summaryLedgerData)
	{

		$collectDebtRequest = $ledger->collectDebtRequest; // Day la yc hach toan

		$status = $collectDebtRequest->status;

		// Neu yc co trang thai la `CAN KIEM TRA LAI` thi giu nguyen
		if ($status != CollectDebtEnum::REQUEST_STT_CAN_HOAN_THANH_VA_KIEM_TRA) {
			$status = CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH;
		}

		$updatedYeuCau = CollectDebtRequest::query()
			->where('id', $ledger->request_id)
			->update([
				'status'       => $status, 
				'time_updated' => now()->timestamp
			]);

		if (!$updatedYeuCau) {
			mylog(['LOI CAP NHAT YEU CAU' => $updatedYeuCau]);
			throw new Exception('LOI CAP NHAT YEU CAU');
		}

		$planIds = collect($summaryLedgerData)->where('label', CollectDebtEnum::METADATA_LICH_THUC_HIEN_GACH)->pluck('value')->toArray();

		mylog(['Danh sach lich se thuc hien gach la: ' => $planIds]);

		if (!empty($planIds)) {
			if ($collectDebtRequest->payment_method_code == 'MPOS' && $collectDebtRequest->create_from == CollectDebtEnum::REQUEST_LOAI_TRICH_TU_DONG) {
				$wasDeleted = CollectDebtProcessing::query()->where([
					'contract_code' => $collectDebtRequest->contract_code,
					'partner_request_id' => $collectDebtRequest->partner_request_id
				])->delete();

				throw_if( empty($wasDeleted), new Exception('Loi khong the xoa processing: ' . $collectDebtRequest->partner_request_id));
			}

			$wasDeleteReadyRecord = CollectDebtReadyRecord::query()->where(['partner_request_id' => $collectDebtRequest->partner_request_id])->delete();

			$updatedPlans = CollectDebtSchedule::whereIn('id', $planIds)->update([
				'status'       => CollectDebtEnum::SCHEDULE_STT_DA_HOAN_THANH,
				'is_process'   => CollectDebtEnum::SCHEDULE_PROCESS_DA_XU_LY,
				'time_updated' => now()->timestamp,
				'time_accounting' => now()->timestamp,
			]);

			if (!$updatedPlans) {
				mylog(['[LOI CAP NHAT LICH THU VE TRANG THAI CUOI]' => $updatedPlans]);
				throw new Exception('[LOI CAP NHAT LICH THU VE TRANG THAI CUOI]');
			}
		}
	}
} // End class
