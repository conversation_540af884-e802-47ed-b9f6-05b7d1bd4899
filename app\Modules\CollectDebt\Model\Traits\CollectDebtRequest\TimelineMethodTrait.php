<?php

namespace App\Modules\CollectDebt\Model\Traits\CollectDebtRequest;



trait TimelineMethodTrait
{
  public function getTimelineAttribute(): string
  {
    $timeline = '';

    if (!empty($this->time_created)) {
      $p = json_decode($this->created_by, true);

      $timeline .= sprintf(
        'Tạo: %s - %s;', 
        date('H:i:s, d/m/Y', $this->time_created), 
        $p['username'] ?? 'N/A'
      );
    }

    if (!empty($this->time_approved)) {
      if ($this->isAutoDebt() || $this->isTrichTayThuGoc()) {
        $p = json_decode($this->approved_by, true);
      
        $timeline .= sprintf(
          'Duyệt: %s - %s;', 
          date('H:i:s, d/m/Y', $this->time_approved),
          $p['username'] ?? 'N/A'
        );
      }
      

      if ($this->isTrichTayGiamPhi()) {
        if ( !empty($this->time_approved) ) {
          $p = json_decode($this->approved_by, true);
          $timeline .= sprintf(
            'Duyệt B1: %s - %s;', 
            date('H:i:s, d/m/Y', $this->time_approved),
            $p['username'] ?? 'N/A'
          );
        }

        if ( !empty($this->isThoigianDuyet2()) && !empty($this->isNguoiDuyet2())) {
          $p = $this->isNguoiDuyet2();

          if (is_string($p)) {
            $p = json_decode($p, true);
          }
          
          $timeline .= sprintf(
            'Duyệt B2: %s - %s;', 
            date('H:i:s, d/m/Y', $this->isThoigianDuyet2()),
            $p['username'] ?? 'N/A'
          );
        }
      }
    }

    if (!empty($this->time_updated)) {
      $p = json_decode($this->updated_by, true);

      $timeline .= sprintf(
        'Cập nhật: %s - %s;', 
        date('H:i:s, d/m/Y', $this->time_updated), 
        $p['username'] ?? 'N/A'
      );
    }

    if (!empty($this->time_sended)) {
      $p = json_decode($this->sended_by, true);

      $timeline .= sprintf(
        'Gửi lệnh: %s;', 
        date('H:i:s, d/m/Y', $this->time_sended),
      );
    }

		if (!empty($this->time_checked)) {
      $timeline .= sprintf(
        'K.tra cuối: %s;', 
        date('H:i:s, d/m/Y', $this->time_checked),
      );
    }

    if (!empty($this->time_canceled_payment)) {
      $p = json_decode($this->canceled_payment_by, true);

      $timeline .= sprintf(
        'Từ chối TT: %s - %s;', 
        date('H:i:s, d/m/Y', $this->time_canceled_payment),
        $p['username'] ?? 'N/A'
      );
    }

    if (!empty($this->time_canceled)) {
      $p = json_decode($this->canceled_by, true);

      $timeline .= sprintf(
        'Hủy TT: %s - %s;', 
        date('H:i:s, d/m/Y ', $this->time_canceled),
        $p['username'] ?? 'N/A'
      );
    }


    // if (!empty($this->time_completed_recheck)) {
    //   $p = json_decode($this->completed_recheck_by, true);

    //   $timeline .= sprintf(
    //     'K.Tra: %s - %s;',  
    //     date('H:i:s, d/m/Y', $this->time_completed_recheck),
    //     $p['username'] ?? 'N/A'
    //   );
    // }

    if (!empty($this->time_receivered)) {
      $p = json_decode($this->receivered_by, true);

      $timeline .= sprintf(
        'Nhận TT: %s;', 
        date('H:i:s, d/m/Y', $this->time_receivered),
      );
    }

    if (!empty($this->time_recored)) {
      $p = json_decode($this->recored_by, true);
      $timeline .= sprintf(
        'Ghi sổ: %s;',  
        date('H:i:s, d/m/Y', $this->time_recored),
      );
    }

    if (!empty($this->time_completed)) {
      $p = json_decode($this->completed_by, true);
      $timeline .= sprintf(
        'H.Thành: %s;', 
        date('H:i:s, d/m/Y', $this->time_completed),
      );
    }

    return $timeline;
  }
} // End class
