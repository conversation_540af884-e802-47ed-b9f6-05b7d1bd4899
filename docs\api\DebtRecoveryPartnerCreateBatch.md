# API Batch Create Debt Recovery Partner

## Endpoint
```
POST /api/DebtRecoveryPartnerCreateBatch
```

## <PERSON><PERSON> tả
API này cho phép tạo nhiều bản ghi công nợ đối tác cùng lúc (batch processing). API sẽ xử lý từng item một cách độc lập và trả về kết quả chi tiết cho từng item.

## Yêu cầu
- Chỉ hoạt động trên môi trường development (không phải production)
- Cần bật debug mode (`APP_DEBUG=true`)

## Request Structure

### Headers
```
Content-Type: application/json
Authorization: Bearer {token}
```

### Body
```json
{
  "checksum": "859716a26a9e5e0c7ade22f5a8b58d3b",
  "channel_code": "WEBPARTNER",
  "time_request": **********,
  "version": "1.0",
  "data": [
    {
      "payment_channel_code": "MPOS",
      "payment_method_code": "MPOS",
      "payment_account_id": "TEST-HWBXGW-L6",
      "partner_request_id": "NL25090860856",
      "partner_transaction_id": "*********",
      "amount_payment": "2500000",
      "amount_receiver": "2500000",
      "fee": "0",
      "request_exists": "1",
      "response": "{}",
      "other_data": "{}",
      "description": "done",
      "created_by": "1",
      "time_created": ""
    },
    {
      "payment_channel_code": "MPOS",
      "payment_method_code": "MPOS",
      "payment_account_id": "TEST-HWBXGW-L6",
      "partner_request_id": "NL25090860857",
      "partner_transaction_id": "*********",
      "amount_payment": "3000000",
      "amount_receiver": "3000000",
      "fee": "0",
      "request_exists": "1",
      "response": "{}",
      "other_data": "{}",
      "description": "done",
      "created_by": "1",
      "time_created": ""
    }
  ]
}
```

### Validation Rules
- `data`: required, array, minimum 1 item
- `data.*.payment_channel_code`: required, string, max 50 chars
- `data.*.payment_method_code`: required, string, max 50 chars
- `data.*.payment_account_id`: required, string, max 50 chars
- `data.*.partner_request_id`: required if payment_method_code is MPOS, string, max 50 chars
- `data.*.partner_transaction_id`: nullable, string, max 50 chars
- `data.*.amount_payment`: required, numeric, min 0
- `data.*.amount_receiver`: required, numeric, min 0
- `data.*.fee`: nullable, numeric, min 0
- `data.*.request_exists`: required, must be 1 or 2
- `data.*.response`: nullable, valid JSON
- `data.*.other_data`: nullable, valid JSON
- `data.*.description`: nullable, string, max 255 chars
- `data.*.created_by`: nullable, string, max 255 chars
- `data.*.time_created`: nullable

## Response Structure

### Success Response (200)
```json
{
  "status": true,
  "message": "Success",
  "data": {
    "total_processed": 2,
    "success_count": 2,
    "error_count": 0,
    "results": [
      {
        "index": 0,
        "partner_request_id": "NL25090860856",
        "status": "success",
        "data": {
          "id": 123,
          "payment_channel_code": "MPOS",
          "payment_method_code": "MPOS",
          "payment_account_id": "TEST-HWBXGW-L6",
          "partner_request_id": "NL25090860856",
          "partner_transaction_id": "*********",
          "amount_payment": 2500000,
          "amount_receiver": 2500000,
          "fee": 0,
          "request_id": 456
        }
      },
      {
        "index": 1,
        "partner_request_id": "NL25090860857",
        "status": "success",
        "data": {
          "id": 124,
          "payment_channel_code": "MPOS",
          "payment_method_code": "MPOS",
          "payment_account_id": "TEST-HWBXGW-L6",
          "partner_request_id": "NL25090860857",
          "partner_transaction_id": "*********",
          "amount_payment": 3000000,
          "amount_receiver": 3000000,
          "fee": 0,
          "request_id": 457
        }
      }
    ],
    "errors": []
  }
}
```

### Partial Success Response (200)
```json
{
  "status": true,
  "message": "Success",
  "data": {
    "total_processed": 2,
    "success_count": 1,
    "error_count": 1,
    "results": [
      {
        "index": 0,
        "partner_request_id": "NL25090860856",
        "status": "success",
        "data": {
          "id": 123,
          "request_id": 456
        }
      }
    ],
    "errors": [
      {
        "index": 1,
        "partner_request_id": "NL25090860857",
        "error": "Yêu cầu thu hồi `NL25090860857` không tồn tại"
      }
    ]
  }
}
```

### Validation Error Response (422)
```json
{
  "status": false,
  "message": "The given data was invalid.",
  "errors": {
    "data.0.payment_account_id": [
      "The data.0.payment_account_id field is required."
    ]
  }
}
```

### Environment Error Response (400)
```json
{
  "status": false,
  "message": "Chỉ được phép chạy trên môi trường dev"
}
```

## Khác biệt với API đơn

1. **Cấu trúc dữ liệu**: 
   - API đơn: `data` là object
   - API batch: `data` là array chứa nhiều objects

2. **Xử lý lỗi**:
   - API đơn: Dừng ngay khi có lỗi
   - API batch: Tiếp tục xử lý các item khác, trả về chi tiết lỗi

3. **Response**:
   - API đơn: Trả về dữ liệu của 1 record
   - API batch: Trả về thống kê và chi tiết từng record

## Lưu ý
- API này chỉ hoạt động trong môi trường development
- Mỗi item trong batch được xử lý độc lập
- Nếu một item lỗi, các item khác vẫn được xử lý tiếp
- Kết quả trả về bao gồm cả thành công và lỗi
