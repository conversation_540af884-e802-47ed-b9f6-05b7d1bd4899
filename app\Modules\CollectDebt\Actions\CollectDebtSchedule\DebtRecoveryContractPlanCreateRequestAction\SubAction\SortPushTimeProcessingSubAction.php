<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction;

use Illuminate\Support\Facades\DB;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;

class SortPushTimeProcessingSubAction
{
	public function run($profileId)
	{
		$collectDebtProcessing = CollectDebtProcessing::query()->where('profile_id', $profileId)->get();

		$processingSorted = $collectDebtProcessing->sort(function ($a, $b) {
			// Rule 1: partner_code
			if ($a['partner_code'] !== $b['partner_code']) {
				return $a['partner_code'] === 'NEXTLEND' ? -1 : 1;
			}
			// Rule 2: loan_times
			return $a['loan_times'] <=> $b['loan_times'];
		})->values();

		$ids = collect($processingSorted)->pluck('id')->implode(',');
		$cases = '';

		foreach ($processingSorted as $index => $p) {
			$cases .= sprintf(' WHEN %d THEN %d ',
				$p->id,
				now()->addMinutes($p->mark_up)->addMinutes($index + 2)->addSeconds($p->loan_times)->timestamp
			);
		}

		$sql = "UPDATE debt_recovery_processing 
        SET push_time = CASE id 
          $cases
        END
			WHERE profile_id = $profileId AND id IN ($ids)";

		$r = DB::statement($sql);

		if (!$r) {
			throw new \Exception('Loi cap nhat push time cho profile: ' . $profileId);
		}

		return $r;
	}
}
