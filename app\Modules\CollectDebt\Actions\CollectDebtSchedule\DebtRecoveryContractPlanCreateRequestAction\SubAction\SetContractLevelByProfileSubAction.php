<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\SubAction;

use Illuminate\Support\Str;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtPlan;
use App\Modules\CollectDebt\Model\CollectDebtContractLevel;

class SetContractLevelByProfileSubAction
{
	public function run($profileId): bool
	{
		return true;
		
		$plans = CollectDebtPlan::query()
			->with('collectDebtShareOnly:contract_code,partner_code')
			->where("rundate", '<=', intval(date("Ymd")))
			->where("profile_id", $profileId)
			->where('is_process', CollectDebtEnum::SCHEDULE_PROCESS_CHUA_XU_LY)
			->where('status', CollectDebtEnum::SCHEDULE_STT_MOI)
			->orderBy('rundate', 'DESC')
			->select(['contract_code', 'rundate', 'profile_id'])
			->get();

		if ($plans->isEmpty()) {
			return false;
		}


		$plans = $plans->unique("contract_code");

		$plansSorted = $plans->map(function (CollectDebtPlan $collectDebtPlan) {
			$loanTimes = Str::afterLast($collectDebtPlan->contract_code, '-L');
			$collectDebtPlan->loan_times = intval($loanTimes);
			$collectDebtPlan->partner_code = $collectDebtPlan->collectDebtShareOnly->partner_code;
			return $collectDebtPlan;
		})->sort(function ($a, $b) {
			// Rule 1: partner_code
			if ($a['partner_code'] !== $b['partner_code']) {
				return $a['partner_code'] === 'NEXTLEND' ? -1 : 1;
			}
			// Rule 2: loan_times
			return $a['loan_times'] <=> $b['loan_times'];
		})->values();


		foreach ($plansSorted as $key => $plan) {
			$loanTimes = $plan->loan_times;
			$partnerCode = $plan->partner_code;

			$scheduleAt = now();

			if ($partnerCode == 'NEXTLEND') {
				$scheduleAt = now();
			}

			if ($partnerCode == 'TNEX') {
				$scheduleAt = now()->addMinutes(3)->addSeconds(10);
			}

			if ($loanTimes > 1 && $loanTimes <= 30) {
				$scheduleAt = $scheduleAt->addSeconds($loanTimes * 5);
			}

			if ($loanTimes > 30) {
				$scheduleAt = $scheduleAt->addSeconds(150)->addSeconds($loanTimes);
			}

			$p = [
				'profile_id' => $plan->profile_id,
				'contract_code' => $plan->contract_code,
				'rundate' => $plan->rundate,
				'loan_times' => $loanTimes,
				'partner_code' => $partnerCode,
				'level' => $key + 1,
				'is_send_cmd' => 0,
				'created_at' => now(),
				'updated_at' => now(),
				'schedule_send_at' => $scheduleAt
			];

			$created = CollectDebtContractLevel::query()->firstOrCreate([
				'profile_id' => $plan->profile_id,
				'contract_code' => $plan->contract_code,
			], $p);

			if (!$created) {
				throw new \Exception('Loi insert contract level, planId: ' . $plan->id);
			}
		}

		return true;
	}
} // End class