<?php

namespace App\Lib;

use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Lib\ExtraCustomHelper;
use App\Modules\CollectDebt\Model\CollectDebtSchedule;
use App\Modules\CollectDebt\Model\CollectDebtSetting;
use App\Modules\CollectDebt\Model\Traits\Common\StandardizedDataFilter;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Throwable;


class Helper
{
	use ExtraCustomHelper;

	/**
	 * Get client ip address
	 * @return mixed|string
	 */
	public static function getClientIp()
	{
		$ipAddress = '';
		if (isset($_SERVER['HTTP_CLIENT_IP'])) {
			$ipAddress = $_SERVER['HTTP_CLIENT_IP'];
		} elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
			$ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
		} elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
			$ipAddress = $_SERVER['HTTP_X_FORWARDED'];
		} elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
			$ipAddress = $_SERVER['HTTP_FORWARDED_FOR'];
		} elseif (isset($_SERVER['HTTP_FORWARDED'])) {
			$ipAddress = $_SERVER['HTTP_FORWARDED'];
		} elseif (isset($_SERVER['REMOTE_ADDR'])) {
			$ipAddress = $_SERVER['REMOTE_ADDR'];
		} else {
			$ipAddress = 'UNKNOWN';
		}
		return $ipAddress;
	}


	public static function priceFormat($price, string $unit = '')
	{
		$price = number_format($price, 0, '', ',');
		if (!empty($unit)) {
			$price .= $unit;
		}

		return $price;
	}

	public static function numberFormat($number = 0)
	{
		$number = (float) $number;

		$number = number_format($number, 0, '', '.');
		return $number;
	}


	public static function traceError(Throwable  $th): string
	{
    $file = $th->getFile();
    $paths = explode('\\', $file);

		$errorMessage =  sprintf('[Error: %s] - [Line: %s] - [File: %s]', $th->getMessage(), $th->getLine(), Arr::last($paths));
		return $errorMessage;

		$env = env('APP_ENV');

		if (!$env || $env == 'local') {
			$errorMessage =  sprintf('[Error: %s] - [Line: %s] - [File: %s]', $th->getMessage(), $th->getLine(), $th->getFile());
		}
		

		if ($env != 'local') {
			$errorMessage = sprintf('[Error: %s]', $th->getMessage());
		}


		return $errorMessage;
	}

	public static function convertPriceToNumber($price = null)
	{
		if (empty($price)) {
			return 0;
		}

		$number = str_replace([',', '.'], '', $price);
		return (float) $number;
	}

	public static function cleanXss($value = '')
	{
		if (!is_array($value)) {
			$value = htmlentities(strip_tags($value ?? ''));
			return trim($value);
		}

		if (is_array($value)) {
			foreach ($value as &$val) {
				$val = htmlentities(strip_tags($val ?? ''));
				$val = trim($val);
			}
		}

		return $value;
	}

	public static function isInsideTime($hours = 22): bool 
	{
		$time = env('API_REQUEST_DEBT_CUTOFF_TIME', '22:00:00');
		$cutOffTime = now()->copy()->setTimeFromTimeString($time); // 22:00
		return now()->lte($cutOffTime);
	}

	public static function isCutOffTime(): bool
	{
		return !self::isInsideTime();
	}

	public static function getCutOffTime(): string
	{
		return env('API_REQUEST_DEBT_CUTOFF_TIME', '22:00:00');
	}

	public static function getCronJobUser(): string
	{
		return StandardizedDataFilter::getUserAdminStructCompact([]);
	}

	public static function getPlanCompact(Collection $plans, bool $isNoNeedJson=false)
	{
		if ($isNoNeedJson) {
			return [];
			// return $plans->map(function (CollectDebtSchedule $plan) {
			// 	return StandardizedDataFilter::getPlanCompactAttribute($plan);
			// })->toArray();
		}

		return json_encode([]);
		
		return $plans->map(function (CollectDebtSchedule $plan) {
			return StandardizedDataFilter::getPlanCompactAttribute($plan);
		})->toJson(JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
	}

	public static function canFakeRecheckDebt(): bool {
		return CollectDebtSetting::isEnableSetting('IS_FAKE_RECHECK');
	}

	public static function canFakeAmountMpos($mposMcId=''): bool {
		if (empty($mposMcId)) {
			return false;
		}
		
		if (config('app.env') != 'production' && config('app.debug') && Str::contains(env('LIST_MC_CAN_FAKE_SO_DU'), $mposMcId)) {
			return true;
		}

		return false;
	}

	public static function getSoLanVay(string $contractCode) 
	{
		$soLan = Str::afterLast($contractCode, '-L') ?? 0;
		$soLan = intval($soLan);

		return $soLan;
	}
} // End class