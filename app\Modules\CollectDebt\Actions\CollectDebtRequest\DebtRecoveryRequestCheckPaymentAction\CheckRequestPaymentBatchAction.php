<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction;

use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Collection;
use App\Modules\CollectDebt\Model\CollectDebtRequest;
use App\Modules\CollectDebt\Model\CollectDebtProcessing;
use App\Modules\CollectDebt\Actions\BatchProcessingAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\SubAction\CheckRequestViaMposSubAction;

class CheckRequestPaymentBatchAction extends BatchProcessingAction
{
	public $timeout = 90;

	public function __construct()
	{
		ini_set('max_execution_time', $this->timeout);
	}

	public function run()
	{
		$canRunJob = parent::canRunJob();

		if (!$canRunJob) {
			return 'job will for nextday';
		}

		$r = CollectDebtProcessing::query()
			->where('is_sent_request', 1)
			->where('checked_at', '<=', now())
			->orderBy('checked_at', 'ASC')
			->limit(config('nextlend.BATCHING_LIMIT'))
			->pluck('id')
			->toArray();

		if (empty($r)) {
			return ['msg' => 'No data for CheckRequestPaymentBatchAction'];
		}

		$ranges = array_chunk($r, $this->batchSize);
		$processResult = $this->processBatch($ranges);

		return $processResult;
	}


	private function processBatch($ranges)
	{
		$client = parent::createHttpClient($this->timeout-10);

		$baseUrl = config('app.url');
		
		$requests = function () use ($ranges, $baseUrl) {
			foreach ($ranges as $r) {
				$url = sprintf('%s/HandleCheckMposRequest?ids=%s', $baseUrl, implode(',', $r));
				yield $r => new Request('GET', $url);
			}
		};

		$pool = new Pool($client, $requests(), [
			'concurrency' => $this->batchCount,
			'fulfilled' => function ($response, $r) {
				// $body = (string)$response->getBody();
				
			},
			'rejected' => function (\Throwable $reason, $r) {
				$msg = "[CheckRequest --->range error: " . json_encode($r) . '] failed: ' . $reason->getMessage();
				Log::info($msg);
			},
		]);

		$promise = $pool->promise();
		$promise->wait();

		unset($pool, $promise);

		return ['msg' => 'all done'];
	}

	public function closeProcessing(Collection $listProcessing)
	{
		$listIds = $listProcessing->pluck('partner_request_id')->toArray();

		return CollectDebtProcessing::query()->whereIn('partner_request_id', $listIds)->update([
			'checked_at' => DB::raw("DATE_ADD(checked_at, INTERVAL 60 MINUTE)")
		]);
	}

	public function HandleCheckMposRequest()
	{
		$ids = request()->get('ids');
		$listIds = array_map('intval', explode(',', $ids));

		$listProcessing = CollectDebtProcessing::query()->with('collectDebtRequest.collectDebtPartner')
																										->whereIn('id', $listIds)
																										->orderBy('checked_at', 'ASC')
																										->get();
		
		if ($listProcessing->isEmpty()) {
			return 'No data';
		}

		foreach ($listProcessing as $p) {
			try {
				$collectDebtRequest = $p->collectDebtRequest;
				$this->handle($collectDebtRequest);
			}catch(\Throwable $th) {
				$msg = "[CheckRequest--Fail: --->$p->partner_request_id] failed: " . $th->getMessage();
				Log::info($msg);
			}
		}

		$this->closeProcessing($listProcessing);

		return true;
	}

	public function handle(CollectDebtRequest $collectDebtRequest) {
		$request = request();

		if (!$collectDebtRequest->collectDebtPartner) {
			$collectDebtRequestChecked = app(CheckRequestViaMposSubAction::class)->run($collectDebtRequest, $request);
			$collectDebtRequest->mpos_debt_result = $collectDebtRequestChecked;
		}

		// có partner rồi thì update timecheck
		if ($collectDebtRequest->collectDebtPartner) {
			$collectDebtRequest->forceFill(['time_checked' => time()])->update();
		}

		return $collectDebtRequest->partner_request_id;
	}
}  // End class