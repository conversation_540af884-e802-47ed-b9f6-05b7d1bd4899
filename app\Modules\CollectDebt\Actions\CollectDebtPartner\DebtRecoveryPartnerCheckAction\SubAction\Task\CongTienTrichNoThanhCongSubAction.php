<?php

namespace App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\SubAction\Task;

use Exception;
use App\Lib\Helper;
use App\Lib\TelegramAlert;
use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use App\Modules\CollectDebt\Model\CollectDebtRequest;

class CongTienTrichNoThanhCongSubAction
{
	public function run(CollectDebtRequest $collectDebtRequest, $soTienTrichNoThanhCong = 0)
	{
		$errorMessage = '';

		if ($collectDebtRequest->isFinalPaymentStatus()) {
			$errorMessage = sprintf('Yêu cầu `%s` đã về trạng thái nhận tiền. Không thể cộng tiền thêm', $collectDebtRequest->partner_request_id);
		}

		if ($collectDebtRequest->status == CollectDebtEnum::REQUEST_STT_DA_HOAN_THANH) {
			$errorMessage = sprintf('Status yc `%s` đã là  thành công cuối cùng, không thể cộng tiền thêm', $collectDebtRequest->partner_request_id);
		}

		if ($collectDebtRequest->status == CollectDebtEnum::REQUEST_STT_TU_CHOI) {
			$errorMessage = sprintf('Status yc `%s` đã là từ chối cuối cùng, không thể cộng tiền thêm', $collectDebtRequest->partner_request_id);
		}
		
		if ( !empty($errorMessage) ) {
			TelegramAlert::alertTienVe($errorMessage);
			throw new Exception($errorMessage);
		}

		$paramUpdate = [
			'amount_receiver'        => $soTienTrichNoThanhCong,
			'amount_payment'				 => $soTienTrichNoThanhCong,
			'fee'                    => 0,
			'time_receivered'        => now()->timestamp,
			'checked_by'             => Helper::getCronJobUser(),
			'status_payment'         => CollectDebtEnum::REQUEST_STT_PM_DA_NHAN_KET_QUA,
			'time_completed'         => now()->timestamp,
			'completed_by'           => Helper::getCronJobUser(),
		];

		// Cập nhật cộng tiền
		$updatedCongTien = CollectDebtRequest::where('id', $collectDebtRequest->id)->update($paramUpdate);

		if (!$updatedCongTien) {
			$errorMessage = sprintf('Lỗi cập nhật cộng tiền vào yêu cầu `%s`', $collectDebtRequest->partner_request_id);
			throw new Exception($errorMessage);
		}

		return $collectDebtRequest;
	}
} // End class