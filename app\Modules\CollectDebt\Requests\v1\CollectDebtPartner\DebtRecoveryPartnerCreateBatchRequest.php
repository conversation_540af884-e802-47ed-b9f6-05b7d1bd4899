<?php

namespace App\Modules\CollectDebt\Requests\v1\CollectDebtPartner;

use App\Modules\CollectDebt\Enums\CollectDebtEnum;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class DebtRecoveryPartnerCreateBatchRequest extends FormRequest
{
  /**
   * Determine if the user is authorized to make this request.
   *
   * @return bool
   */
  public function authorize()
  {
    return true;
  }

  /**
   * Get the validation rules that apply to the request.
   *
   * @return array
   */
  public function rules()
  {
    return [
      'data' => ['required', 'array', 'min:1'],
      'data.*.payment_channel_code' => ['required', 'string', 'max:50'],
      'data.*.payment_method_code' => ['required', 'string', 'max:50'],
      'data.*.payment_account_id' => ['required', 'string', 'max:50'], // Nếu là MPOS thì phải là Mã HĐ
      'data.*.partner_request_id' => ['required_if:data.*.payment_method_code,MPOS', 'string', 'max:50'],
      'data.*.partner_transaction_id' => ['nullable', 'string', 'max:50'],
      'data.*.amount_payment' => ['required', 'numeric', 'min:0'],
      'data.*.amount_receiver' => ['required', 'numeric', 'min:0'],
      'data.*.fee' => ['nullable', 'numeric', 'min:0'],
      'data.*.request_exists' => ['required', Rule::in([1, 2])],
      'data.*.response' => ['nullable', 'json'],
      'data.*.other_data' => ['nullable', 'json'],
      'data.*.description' => ['nullable', 'string', 'max:255'],
      'data.*.created_by' => ['nullable', 'string', 'max:255'],
      'data.*.time_created' => ['nullable'],
    ];
  }

  protected function prepareForValidation()
  {
    $params = $this->all();
    
    if (isset($params['data']) && is_array($params['data'])) {
      foreach ($params['data'] as $index => $item) {
        // Set default values for each item
        $params['data'][$index]['time_created'] = $item['time_created'] ?? time();
        $params['data'][$index]['status'] = CollectDebtEnum::PARTNER_STT_CHUA_XU_LY;
        
        if (empty($item['created_by'])) {
          $params['data'][$index]['created_by'] = json_encode(['id' => 'cronjob', 'username' => 'cronjob', 'mobile' => 'cronjob']);
        }
        
        // Set default amount_payment if not provided
        if (!isset($item['amount_payment'])) {
          $params['data'][$index]['amount_payment'] = $item['amount_receiver'] ?? 0;
        }
      }
    }

    $this->merge($params);
  }
} // End class
